import { <PERSON><PERSON>sister, TopScorer } from "@pro-clubs-manager/shared-dtos";
import { ClientSession, Types } from "mongoose";
import logger from "../config/logger";
import { NotFoundError, QueryFailedError } from "../errors";
import { ILeagueRepository } from "../interfaces/league/league-repository.interface";
import { IPlayerRepository } from "../interfaces/player/player-repository.interface";
import { ITeamRepository } from "../interfaces/team/team-repository.interface";
import League, { ILeague } from "../models/league";
import Game from "../models/game/game";
import { inject, injectable } from "tsyringe";

@injectable()
export class LeagueRepository implements ILeagueRepository {
  constructor(
    @inject("IPlayerRepository") private playerRepository: IPlayerRepository,
    @inject("ITeamRepository") private teamRepository: ITeamRepository
  ) {}
  async getAllLeagues(): Promise<ILeague[]> {
    try {
      const leagues = await League.find();
      return leagues;
    } catch (error: any) {
      logger.error(error.message);
      throw new QueryFailedError(`Failed to get all leagues`);
    }
  }

  async getLeagueById(id: string | Types.ObjectId, session?: ClientSession): Promise<ILeague> {
    try {
      const league = await League.findById(id, {}, { session });
      if (!league) {
        throw new NotFoundError(`League with id ${id} not found`);
      }
      return league;
    } catch (error: any) {
      if (error instanceof NotFoundError) {
        throw error; // Re-throw NotFoundError
      } else {
        logger.error(error.message);
        throw new QueryFailedError(`Failed to get league by id ${id}`);
      }
    }
  }

  async isLeagueNameExists(name: string): Promise<boolean> {
    try {
      const exists = await League.exists({ name });
      return !!exists;
    } catch (error: any) {
      logger.error(error.message);
      throw new QueryFailedError(`Failed to check if league name exists`);
    }
  }

  async createLeague(name: string, imgUrl?: string | undefined, session?: ClientSession): Promise<ILeague> {
    try {
      const league = (await League.create({ name, imgUrl }, { session }))[0];
      return league;
    } catch (error: any) {
      logger.error(error.message);
      throw new QueryFailedError(`Failed to create league with name ${name}`);
    }
  }

  async deleteLeague(id: string | Types.ObjectId, session?: ClientSession): Promise<void> {
    try {
      const league = await League.findByIdAndDelete(id, { session });
      if (!league) {
        throw new NotFoundError(`League with id ${id} not found`);
      }
    } catch (e: any) {
      if (e instanceof NotFoundError) {
        throw e;
      } else {
        logger.error(e.message);
        throw new QueryFailedError(`Failed to delete league with id ${id}`);
      }
    }
  }
  async removeTeamFromLeague(leagueId: Types.ObjectId, teamId: Types.ObjectId, session?: ClientSession | undefined): Promise<void> {
    try {
      const league = await League.updateOne({ _id: leagueId }, { $pull: { teams: teamId } }, { session });
      if (!league) {
        throw new NotFoundError(`League with id ${leagueId} not found`);
      }
    } catch (e: any) {
      if (e instanceof NotFoundError) {
        throw e;
      } else {
        logger.error(e.message);
        throw new QueryFailedError(`Failed to remove team from league with id ${leagueId}`);
      }
    }
  }

  async calculateLeagueTopScorers(leagueId: string, limit: number, session?: ClientSession): Promise<TopScorer[]> {
    try {
      // Get current season number for the league
      const currentSeasonNumber = await this.getCurrentSeasonNumber(leagueId);
      logger.info(`Calculating top scorers for league ${leagueId}, season ${currentSeasonNumber}`);

      // Use the same proven aggregation method as game repository
      const playerStats = await Game.aggregate([
        {
          $match: {
            league: new Types.ObjectId(leagueId),
            seasonNumber: currentSeasonNumber,
            result: { $exists: true } // Only completed games
          }
        },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
            totalGames: { $sum: 1 }
          }
        },
        { $sort: { totalGoals: -1, totalGames: 1 } },
        { $limit: limit }
      ], { session });

      logger.info(`Found ${playerStats.length} players with stats for league ${leagueId}, season ${currentSeasonNumber}`);

      // Now get player and team information for each player
      const topScorers: TopScorer[] = [];

      for (const stats of playerStats) {
        try {
          // Get player information
          const player = await this.playerRepository.getPlayerById(stats._id, session);

          // Get team information
          let teamInfo = null;
          if (player.team) {
            teamInfo = await this.teamRepository.getTeamById(player.team, session);
          }

          const goalsPerGame = stats.totalGames > 0 ? stats.totalGoals / stats.totalGames : 0;

          topScorers.push({
            playerId: player._id.toString(),
            playerName: player.name,
            teamId: teamInfo?._id.toString() || '',
            teamName: teamInfo?.name || 'Free Agent',
            position: player.position,
            playerImgUrl: player.imgUrl,
            games: stats.totalGames,
            goals: stats.totalGoals,
            goalsPerGame: goalsPerGame
          });
        } catch (playerError: any) {
          logger.warn(`Could not get player info for ${stats._id}:`, playerError.message);
          // Skip this player if we can't get their info
          continue;
        }
      }

      logger.info(`Successfully calculated top scorers: ${topScorers.length} players`);
      return topScorers;
    } catch (e: any) {
      logger.error(`Error calculating top scorers for league ${leagueId}:`, e.message);
      throw new QueryFailedError(`Failed to calculate top scorers for league with id ${leagueId}`);
    }
  }

  async calculateLeagueTopAssisters(leagueId: string, limit: number, session?: ClientSession): Promise<TopAssister[]> {
    try {
      // Get current season number for the league
      const currentSeasonNumber = await this.getCurrentSeasonNumber(leagueId);
      logger.info(`Calculating top assisters for league ${leagueId}, season ${currentSeasonNumber}`);

      // Use the same proven aggregation method as game repository
      const playerStats = await Game.aggregate([
        {
          $match: {
            league: new Types.ObjectId(leagueId),
            seasonNumber: currentSeasonNumber,
            result: { $exists: true } // Only completed games
          }
        },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
            totalGames: { $sum: 1 }
          }
        },
        { $sort: { totalAssists: -1, totalGames: 1 } },
        { $limit: limit }
      ], { session });

      logger.info(`Found ${playerStats.length} players with assist stats for league ${leagueId}, season ${currentSeasonNumber}`);

      // Now get player and team information for each player
      const topAssisters: TopAssister[] = [];

      for (const stats of playerStats) {
        try {
          // Get player information
          const player = await this.playerRepository.getPlayerById(stats._id, session);

          // Get team information
          let teamInfo = null;
          if (player.team) {
            teamInfo = await this.teamRepository.getTeamById(player.team, session);
          }

          const assistsPerGame = stats.totalGames > 0 ? stats.totalAssists / stats.totalGames : 0;

          topAssisters.push({
            playerId: player._id.toString(),
            playerName: player.name,
            teamId: teamInfo?._id.toString() || '',
            teamName: teamInfo?.name || 'Free Agent',
            position: player.position,
            playerImgUrl: player.imgUrl,
            games: stats.totalGames,
            assists: stats.totalAssists,
            assistsPerGame: assistsPerGame
          });
        } catch (playerError: any) {
          logger.warn(`Could not get player info for ${stats._id}:`, playerError.message);
          // Skip this player if we can't get their info
          continue;
        }
      }

      logger.info(`Successfully calculated top assisters: ${topAssisters.length} players`);
      return topAssisters;
    } catch (e: any) {
      logger.error(`Error calculating top assisters for league ${leagueId}:`, e.message);
      throw new QueryFailedError(`Failed to calculate top assisters for league with id ${leagueId}`);
    }
  }

  async calculateAllTimeTopScorers(leagueId: string, limit: number, session?: ClientSession): Promise<TopScorer[]> {
    try {
      // Use game-by-game aggregation method for accurate all-time stats
      const result = await Game.aggregate<TopScorer>(
        [
          { $match: { league: new Types.ObjectId(leagueId) } },
          {
            $project: {
              players: {
                $concatArrays: [
                  { $ifNull: ["$homeTeamPlayersPerformance", []] },
                  { $ifNull: ["$awayTeamPlayersPerformance", []] }
                ]
              }
            }
          },
          { $unwind: "$players" },
          {
            $group: {
              _id: "$players.playerId",
              totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
              totalGames: { $sum: 1 }
            }
          },
          {
            $addFields: {
              goalsPerGame: {
                $cond: {
                  if: { $eq: ["$totalGames", 0] },
                  then: 0,
                  else: { $divide: ["$totalGoals", "$totalGames"] }
                }
              }
            }
          },
          {
            $lookup: {
              from: "players",
              localField: "_id",
              foreignField: "_id",
              as: "player"
            }
          },
          { $unwind: "$player" },
          {
            $lookup: {
              from: "teams",
              localField: "player.team",
              foreignField: "_id",
              as: "team"
            }
          },
          { $unwind: "$team" },
          {
            $project: {
              playerId: "$_id",
              playerName: "$player.name",
              teamId: "$team._id",
              teamName: "$team.name",
              position: "$player.position",
              playerImgUrl: "$player.imgUrl",
              games: "$totalGames",
              goals: "$totalGoals",
              goalsPerGame: 1
            }
          },
          { $sort: { goals: -1 } },
          { $limit: limit }
        ],
        { session }
      );

      return result;
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to calculate all-time top scorers for league with id ${leagueId}`);
    }
  }

  async calculateAllTimeTopAssisters(leagueId: string, limit: number, session?: ClientSession): Promise<TopAssister[]> {
    try {
      return await Game.aggregate<TopAssister>(
        [
          { $match: { league: new Types.ObjectId(leagueId) } },
          {
            $project: {
              players: {
                $concatArrays: [
                  { $ifNull: ["$homeTeamPlayersPerformance", []] },
                  { $ifNull: ["$awayTeamPlayersPerformance", []] }
                ]
              }
            }
          },
          { $unwind: "$players" },
          {
            $group: {
              _id: "$players.playerId",
              totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
              totalGames: { $sum: 1 }
            }
          },
          {
            $addFields: {
              assistsPerGame: {
                $cond: {
                  if: { $eq: ["$totalGames", 0] },
                  then: 0,
                  else: { $divide: ["$totalAssists", "$totalGames"] }
                }
              }
            }
          },
          {
            $lookup: {
              from: "players",
              localField: "_id",
              foreignField: "_id",
              as: "player"
            }
          },
          { $unwind: "$player" },
          {
            $lookup: {
              from: "teams",
              localField: "player.team",
              foreignField: "_id",
              as: "team"
            }
          },
          { $unwind: "$team" },
          {
            $project: {
              playerId: "$_id",
              playerName: "$player.name",
              teamId: "$team._id",
              teamName: "$team.name",
              position: "$player.position",
              playerImgUrl: "$player.imgUrl",
              games: "$totalGames",
              assists: "$totalAssists",
              assistsPerGame: 1
            }
          },
          { $sort: { assists: -1 } },
          { $limit: limit }
        ],
        { session }
      );
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to calculate all-time top assisters for league with id ${leagueId}`);
    }
  }

  /**
   * Get the current season number for a league
   * Uses the highest season number from games in the league
   */
  private async getCurrentSeasonNumber(leagueId: string): Promise<number> {
    try {
      logger.info(`Getting current season number for league: ${leagueId}`);

      const result = await Game.findOne({ league: new Types.ObjectId(leagueId) })
        .sort({ seasonNumber: -1 })
        .select("seasonNumber")
        .lean()
        .exec();

      const seasonNumber = result?.seasonNumber || 1;
      logger.info(`Current season number for league ${leagueId}: ${seasonNumber}`);
      return seasonNumber;
    } catch (error: any) {
      logger.error(`Error getting current season number for league ${leagueId}:`, error);
      // Return 1 as fallback to prevent server crashes
      return 1;
    }
  }

  /**
   * Sync stored player stats with actual game performance data
   * This method fixes discrepancies between stored stats and real game data
   */
  async syncPlayerStatsWithGameData(leagueId: string, session?: ClientSession): Promise<{ updated: number; errors: string[] }> {
    try {
      logger.info(`Starting player stats sync for league ${leagueId}`);

      const currentSeasonNumber = await this.getCurrentSeasonNumber(leagueId);

      // Get aggregated stats from games
      const gameStats = await Game.aggregate([
        {
          $match: {
            league: new Types.ObjectId(leagueId),
            seasonNumber: currentSeasonNumber,
            result: { $exists: true }
          }
        },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
            totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
            totalGames: { $sum: 1 },
            totalPotm: { $sum: { $cond: [{ $eq: ["$players.playerOfTheMatch", true] }, 1, 0] } },
            totalCleanSheets: { $sum: { $cond: [{ $eq: ["$players.cleanSheet", true] }, 1, 0] } },
            avgRating: { $avg: "$players.rating" }
          }
        }
      ], { session });

      let updated = 0;
      const errors: string[] = [];

      // Update each player's stored stats
      for (const stats of gameStats) {
        try {
          const player = await this.playerRepository.getPlayerById(stats._id, session);

          if (player.currentSeason && player.currentSeason.seasonNumber === currentSeasonNumber) {
            // Update the stored stats with the accurate game data
            player.currentSeason.stats.goals = stats.totalGoals;
            player.currentSeason.stats.assists = stats.totalAssists;
            player.currentSeason.stats.games = stats.totalGames;
            player.currentSeason.stats.playerOfTheMatch = stats.totalPotm;
            player.currentSeason.stats.cleanSheets = stats.totalCleanSheets;
            player.currentSeason.stats.avgRating = stats.avgRating || 0;

            await player.save({ session });
            updated++;

            logger.info(`Updated stats for player ${player.name}: ${stats.totalGoals} goals, ${stats.totalAssists} assists, ${stats.totalGames} games`);
          }
        } catch (playerError: any) {
          const errorMsg = `Failed to update player ${stats._id}: ${playerError.message}`;
          logger.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      logger.info(`Player stats sync completed: ${updated} players updated, ${errors.length} errors`);
      return { updated, errors };
    } catch (error: any) {
      logger.error(`Error syncing player stats for league ${leagueId}:`, error);
      throw new QueryFailedError(`Failed to sync player stats for league ${leagueId}`);
    }
  }
}
