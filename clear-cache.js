#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to clear caches after fixing the top scorers calculation
 * This will force a refresh with the new accurate data
 */

const https = require('https');
const http = require('http');

const LEAGUE_ID = '65ecb1eb2f272e434483a821';
const BASE_URL = process.env.SERVER_URL || 'http://localhost:3000';

// Parse the URL to determine if we need HTTP or HTTPS
const isHttps = BASE_URL.startsWith('https://');
const requestModule = isHttps ? https : http;

function makeRequest(path, method = 'POST') {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
        // 'Authorization': 'Bearer your-token-here'
      }
    };

    const req = requestModule.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function clearCaches() {
  console.log('🧹 Clearing caches after top scorers calculation fix...\n');

  try {
    // Clear game caches (includes top scorers, top assists, dashboard)
    console.log('1. Clearing game-related caches...');
    const gameResult = await makeRequest(`/game/clear-cache/${LEAGUE_ID}`);
    if (gameResult.status === 200) {
      console.log('✅ Game caches cleared successfully');
      console.log('   Cleared:', gameResult.data.clearedKeys?.join(', ') || 'all caches');
    } else {
      console.log('❌ Failed to clear game caches:', gameResult.status);
    }

    // Clear league-specific caches
    console.log('\n2. Clearing league-specific caches...');
    const leagueResult = await makeRequest(`/league/${LEAGUE_ID}/clear-cache`);
    if (leagueResult.status === 200) {
      console.log('✅ League caches cleared successfully');
      console.log('   Cleared:', leagueResult.data.clearedCaches?.join(', ') || 'all league caches');
    } else {
      console.log('❌ Failed to clear league caches:', leagueResult.status);
    }

    console.log('\n🎉 Cache clearing completed!');
    console.log('\n📊 The next time you check the dashboard or top scorers,');
    console.log('   they should show the correct goal counts using the new');
    console.log('   game-by-game aggregation method.');
    
  } catch (error) {
    console.error('❌ Error clearing caches:', error.message);
    console.log('\n💡 You can also clear caches manually by:');
    console.log('   1. Making a POST request to /game/clear-cache/' + LEAGUE_ID);
    console.log('   2. Making a POST request to /league/' + LEAGUE_ID + '/clear-cache');
  }
}

// Run the script
clearCaches();
