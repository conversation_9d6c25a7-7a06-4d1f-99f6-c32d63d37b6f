import { LeagueDTO, LeagueTableRow, TopScorer, TopAssister, AddSingleFixtureData, FixtureDTO } from "@pro-clubs-manager/shared-dtos";

export interface ILeagueService {
  getAllLeagues(): Promise<LeagueDTO[]>;
  getLeagueById(leagueId: string): Promise<LeagueDTO>;

  getLeagueTable(leagueId: string): Promise<LeagueTableRow[]>;
  updateLeagueTable(leagueId: string): Promise<void>;
  getLeagueTeamOfTheWeek(leagueId: string, startDate: Date, endDate: Date): Promise<{}>;

  getTopScorers(leagueId: string, limit?: number): Promise<TopScorer[]>;
  getTopAssists(leagueId: string, limit?: number): Promise<TopAssister[]>;
  getAllTimeTopScorers(leagueId: string, limit?: number): Promise<TopScorer[]>;
  getAllTimeTopAssisters(leagueId: string, limit?: number): Promise<TopAssister[]>;

  addLeague(name: string, imgUrl?: string): Promise<LeagueDTO>;
  deleteLeague(leagueId: string): Promise<void>;

  startNewSeason(leagueId: string, startDate: string, endDate?: string): Promise<void>;
  createFixture(leagueId: string, fixtureData: AddSingleFixtureData): Promise<FixtureDTO>;
  generateLeagueFixtures(leagueId: string, startDate: string, fixturesPerWeek: number): Promise<FixtureDTO[]>;
  deleteAllLeagueFixtures(leagueId: string): Promise<void>;

  // Cache management methods
  clearTopScorersCache(leagueId: string): Promise<void>;
  clearTopAssistsCache(leagueId: string): Promise<void>;
  clearAllTimeTopScorersCache(leagueId: string): Promise<void>;
  clearAllTimeTopAssistersCache(leagueId: string): Promise<void>;
  clearLeagueStatsCache(leagueId: string): Promise<void>;
  clearLeagueTableCache(leagueId: string): Promise<void>;
  clearFreeAgentsCache(leagueId: string): Promise<void>;

  // Player stats sync method
  syncPlayerStatsWithGameData(leagueId: string): Promise<{ updated: number; errors: string[] }>;
}
